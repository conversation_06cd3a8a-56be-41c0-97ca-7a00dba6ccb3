import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.SQLException;

/**
 * Successful Database Connection Test
 * Based on test results, using the working URL configuration
 */
public class DatabaseConnectionSuccess {
    
    // Working configuration from test results
    private static final String DB_URL = "************************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "consoleService8*";
    private static final String DB_DRIVER = "com.mysql.cj.jdbc.Driver";
    
    public static void main(String[] args) {
        System.out.println("=== CFW Database Connection (Working Version) ===");
        System.out.println("URL: " + DB_URL);
        System.out.println("User: " + DB_USER);
        System.out.println();
        
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        
        try {
            // Load MySQL driver
            Class.forName(DB_DRIVER);
            System.out.println("SUCCESS: MySQL driver loaded successfully");

            // Establish database connection
            connection = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            System.out.println("SUCCESS: Database connected successfully!");
            
            // Create statement
            statement = connection.createStatement();
            
            // Test 1: Get MySQL version
            System.out.println("\n📊 Database Information:");
            resultSet = statement.executeQuery("SELECT VERSION()");
            if (resultSet.next()) {
                System.out.println("   MySQL Version: " + resultSet.getString(1));
            }
            
            // Test 2: Get current database
            resultSet = statement.executeQuery("SELECT DATABASE()");
            if (resultSet.next()) {
                System.out.println("   Current Database: " + resultSet.getString(1));
            }
            
            // Test 3: Get current time
            resultSet = statement.executeQuery("SELECT NOW()");
            if (resultSet.next()) {
                System.out.println("   Database Time: " + resultSet.getString(1));
            }
            
            // Test 4: Show tables
            System.out.println("\n📋 Database Tables:");
            resultSet = statement.executeQuery("SHOW TABLES");
            int count = 0;
            while (resultSet.next() && count < 10) {
                System.out.println("   " + (++count) + ". " + resultSet.getString(1));
            }
            if (count == 10) {
                System.out.println("   ... (showing first 10 tables)");
            }
            
            // Test 5: Get table count
            resultSet = statement.executeQuery("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'cfw'");
            if (resultSet.next()) {
                System.out.println("\n📈 Total tables in database: " + resultSet.getInt(1));
            }
            
            System.out.println("\nSUCCESS: All tests passed! JDBC connection is working perfectly!");

        } catch (ClassNotFoundException e) {
            System.err.println("ERROR: MySQL driver not found: " + e.getMessage());
        } catch (SQLException e) {
            System.err.println("ERROR: Database error: " + e.getMessage());
            System.err.println("   Error Code: " + e.getErrorCode());
            System.err.println("   SQL State: " + e.getSQLState());
        } catch (Exception e) {
            System.err.println("ERROR: Unexpected error: " + e.getMessage());
        } finally {
            // Close resources
            try {
                if (resultSet != null) resultSet.close();
                if (statement != null) statement.close();
                if (connection != null) {
                    connection.close();
                    System.out.println("\nDatabase connection closed");
                }
            } catch (SQLException e) {
                System.err.println("Error closing connection: " + e.getMessage());
            }
        }
    }
}
