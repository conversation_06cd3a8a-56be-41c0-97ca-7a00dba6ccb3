---
type: "agent_requested"
description: "Apollo配置"
---
这个后端的项目是通过Apollo配置中心完成的，每当你需要查找这个后端项目的配置信息（如数据库、redis、cfw、xxl、ks3、kafka等等信息）的时候，请在下面的apollo配置中心查找：

在cfw-api-common板块中：
vpc.api.host = *************
vpc.api.port = 80
http.time.out = 60000
redis.use.sentinels = false
redis.sentinels = 
redis.sentinels.name = 
redis.maxTotal = 600
redis.maxIdle = 200
redis.timeBetweenEvictionRunsMillis = 20000
redis.minEvictableIdleTimeMillis = 20000
redis.testOnBorrow = true
redis.host = *************
redis.port = 8379
redis.ConnectTimeOut = 5000
redis.password = 123456
redis.database = 3
proxy.host = *************
proxy.port = 8002
etcd.endpoints = http://*************:2379
cfw.rs.bandwidth = 1000
cfw.cloudserver.userdata = #cloud-config\nruncmd:\n    - hostname %s\n    - tcp-mss all 1410\n    - exec cloud-config-agent etcd endpoints *************:2379\n    - password-policy\n    - admin password-expiration 0\n    - exit\n    - logging event on\n    - policy-global\n    - default-action permit\n    - log policy-default\n    - exit\n    - threat-syslog-send-payload on\n    - clock zone china\n    - tcp-seq-check-disable\n    - autosave
cfw.chargeType = Daily
cfw.systemdisk.type = ESSD_SYSTEM_PL0
cfw.systemdisk.size = 50
cfw.instance.type = SE9.2C
cfw.default.name = ksc_knad
cfw.total.eip.map = {"Advanced":10,"Enterprise":20}
cfw.total.acl.num = 5000
cfw.hs.type = VM02
cfw.create.job.interval = 10
cfw.total.eip.num = 20
cfw.cloud.server.name = CfwInstance
cfw.image.Id = cc9406bc-24dc-4760-a6e6-e69eb8979689
cfw.security.group.id = fbb91092-0331-44f6-9393-0b1f176d5234
cfw.elasticsearch.host = ************
cfw.elasticsearch.port = 9200
cfw.elasticsearch.userName = elastic
cfw.elasticsearch.password = FCHWdLN5YfXSlPPy
xxl.job.admin.addresses = http://************:9999/xxl-job-admin
xxl.job.accessToken = H2chdECqRxjME44xnKkC9WpOo5sIAqnV
xxl.job.executor.appname = cfw-api
xxl.job.executor.address = 
xxl.job.executor.ip = 
xxl.job.executor.port = 9209
xxl.job.executor.logpath = /data/ksyun/xxl-job/jobhandler
xxl.job.executor.logretentiondays = 30
ks3.endpoint = ks3-cn-beijing.ksyuncs.com
ks3.ak = AKLTx4BtKSZ3Q2LcM7LOyLMg
ks3.sk = OGi4eYN2N3zOFP1PcjzayQgWtXDEqLNGurxZslb8
ks3.bucket = cfw-test
kafka.url = *************:9093
down.file.url = http://*************:80?Source=ShanShi
current.region = cn-shanghai-3
admin.uid = 73404680
cfw.project.id = 115057
cfw.esgw.in = ************
cfw.esgw.out = ************




在cfw-core-common板块中：
#keystone
keystone.admin.username = admin
keystone.admin.password = ksc
keystone.host = *************
keystone.port = 9202
onepiece.alert.code = KFW_TEST
error.log.send.onepiece = true
send.onepiece.url = http://alarm.inner.sdns.ksyun.com/alarm/receptor
charge.type.comments = {\n    "1": {\n        "name": "预付费(包年包月)",\n        "code": "PrePaidByMonth",\n        "bill": 1\n    },\n    "2": {\n        "name": "按小时配置付费(小时结)",\n        "code": "PrePaidByHour",\n        "bill": 2\n    },\n    "3": {\n        "name": "按月峰值付费(月结)",\n        "code": "PostPaidByPeak",\n        "bill": 3\n    },\n    "5": {\n        "name": "按日配置付费(月结)",\n        "code": "PostPaidByDay",\n        "bill": 5\n    },\n    "6": {\n        "name": "按日峰值付费(月结)",\n        "code": "PostPaidByDailyPeak",\n        "bill": 6\n    },\n    "80": {\n        "name": "按小时带宽实时付费",\n        "code": "BandwidthHourly",\n        "bill": 80\n    },\n    "83": {\n        "name": "地域共享带宽",\n        "code": "PostPaidByRegionPeak",\n        "bill": 83\n    },\n    "84": {\n        "name": "按小时配置付费(月结)",\n        "code": "PostPaidByHour",\n        "bill": 84\n    },\n    "85": {\n        "name": "按日峰值实时付费",\n        "code": "PrePaidByDailyPeak",\n        "bill": 85\n    },\n    "86": {\n        "name": "按日用量实时付费",\n        "code": "DailyPaidByTransfer",\n        "bill": 86\n    },\n    "87": {\n        "name": "按小时配置实时付费",\n        "code": "HourlyInstantSettlement",\n        "bill": 87\n    },\n    "105": {\n        "name": "按月配置付费(月结)",\n        "code": "PostPaidByMonthlyConfig",\n        "bill": 105\n    },\n    "704": {\n        "name": "按月用量付费(月结)",\n        "code": "PostPaidByTransfer",\n        "bill": 704\n    },\n    "801": {\n        "name": "预付费(按次)",\n        "code": "PrepaidByTime",\n        "bill": 801\n    },\n    "805": {\n        "name": "后付费(按次)",\n        "code": "PostpaidByTime",\n        "bill": 805\n    },\n    "807": {\n        "name": "按月增强95峰值付费(月结)",\n        "code": "PostPaidByAdvanced95Peak",\n        "bill": 807\n    },\n    "808": {\n        "name": "按月第5峰均值付费(月结)",\n        "code": "PostPaidBy5thPeakAvg",\n        "bill": 808\n    },\n    "809": {\n        "name": "按小时用量实时付费(小时结)",\n        "code": "PrePaidByHourUsage",\n        "bill": 809\n    }\n}
cfw.deleteInstance.now = false
es_host = http://instance-search-test.inner.sdns.ksyun.com
es_port = 80
user_tag = console
support.region.convert = true
admin.user = 76700badfaa543768bcab2947a3187f9
admin.token = 3690e73fb4bc910f2bf2d753cba101fc3a679e7f3f4614b886f4cc4fcf5cb78369f3330c74dbbe67e0e34ca05e48f7b7
proxy.host = *************
proxy.port = 8002
iam.url = http://************
iam.port = 8008
iam.version = 2015-11-01
iam.region = cn-shanghai-3
rabbitmq.addresses = *************:9112
rabbitmq.username = ksyun
rabbitmq.password = 1qaz@WSX
rabbitmq.vhost = /vpc_dev
regions = cn-beijing-1:BJYZRegionOne,cn-beijing-3:BJZJMRegionOne,cn-beijing-5:BJZJMVPCRegion,cn-beijing-6:TJWQRegion,cn-shanghai-1:SHRegionOne,cn-shanghai-2:SHPBSRegionOne,cn-hongkong-1:HKSTRegionOne,cn-hongkong-2:HKVPCRegion,us-west-1:USCARegionOne,cn-beijing-1-new:BJYZVPCRegion,ap-singapore-1:SGPRegionOne,cn-shanghai-3:SHPBSVpctestRegionOne,cn-guangzhou-1:GZVPCRegion,cn-shanghai-7:SHPBSbasictestRegionOne,cn-qingdaodev-1:QD01vpcdevRegionOne,cn-qingyangtest-1:QYTestRegionOne,cn-qingyangdev-2:QYDevRegionTwo
vpc.regions = cn-shanghai-2:华东1（上海）:false:CN East 1(Shanghai),cn-beijing-6:华北1（北京）:false:CN North 1(Beijing),cn-hongkong-2:香港:false:Hong Kong,ap-singapore-1:新加坡:false:Singapore,cn-guangzhou-1:华南1（广州）:false:CN South 1(Guangzhou),eu-east-1:俄罗斯（莫斯科）:false:Russia (Moscow),cn-taipei-1:台北:false:Taipei,cn-beijing-fin:华北金融1（北京）:false:CN North Finance 1(Beijing),cn-shanghai-fin:华东金融1（上海）:false:CN East Finance 1(Shanghai),cn-qingdao-1:自用（青岛）:false:Self-use (QingDao),cn-north-1-gov:华北政务1（北京）:false:CN North Government 1(Beijing),cn-central-1:华中1（武汉）:false:CN Central 1(Wuhan),cn-southwest-1:西南1（重庆）:false:CN Southwest 1(Chongqing),cn-shanghai-3:华东2（上海）:true:CN East 2(Shanghai),cn-beijing-1-new:北京1区(VPC):true:cn_beijing_1(VPC),cn-qingdaodev-1:自用（青岛2）:false:Self-use (Qingdao2),cn-qingyangtest-1:庆阳1区（测试）:true:Qingyangtest1,cn-qingyangdev-2:庆阳2区（研发自用）:true:cn_qingyangdev_2
trade.product.url = http://************:8180
trade.order.ur = http://************:8280
trade.instance.url = http://************:9380
monitor.host = http://***********:80
spring.datasource.url = *************************************************************************************************************************************
spring.datasource.driver-class-name = com.mysql.cj.jdbc.Driver
spring.datasource.password = consoleService8*
spring.datasource.username = root
kec.service = kec
kec.version = 2016-03-04
kec.url = ecs.inner.sdns.ksyun.com
trade.order.url = http://************:8280
proxy.neutron.appname = security-products
proxy.neutron.token = uF57R0QAEaVhECjmx2hlpk+ZPkliRcT1bXl8rRr/gpZqUOzkCsA0Fbv6sa3Qkep3KmiFwK+A5o5rzM5AiXwU5A==
delete.server = false
create.delete.server. = false
create.delete.server = false
kafka.user = admin
kafka.password = Ksc1234*
kafka.security.protocol = SASL_SSL
kafka.sasl.mechanism = PLAIN
kafka.ssl.truststore.location = D:\kafkacerts\kcm20250625new\mq.anquan.ksyun.com.jks
kafka.ssl.truststore.password = 2Oox>j8!@SG.
kafka.saslEnable = 1
kafka.certPath = cert/mq.anquan.ksyun.com.pem
kafka.cert.md5 = 4BC0B0D46412CEA79367FC4C7CAE817B
etcd.saslEnable = true
etcd.username = root
etcd.password = 123456


在cfw-scheduler-common板块中：
current.region = cn-shanghai-3
#redis
redis.use.sentinels = false
redis.sentinels = 
redis.sentinels.name = 
redis.maxTotal = 600
redis.maxIdle = 200
redis.timeBetweenEvictionRunsMillis = 20000
redis.minEvictableIdleTimeMillis = 20000
redis.testOnBorrow = true
redis.host = *************
redis.port = 8379
redis.ConnectTimeOut = 5000
redis.password = 123456
redis.database = 4


在cfw-message板块中：
# rabbitmq-trade
rabbitmq.trade.addresses = *************:9112
rabbitmq.trade.username = ksyun
rabbitmq.trade.password = 1qaz@WSX
rabbitmq.trade.vhost = /vpc_dev
xxl.job.admin.addresses = http://************:9999/xxl-job-admin
xxl.job.accessToken = H2chdECqRxjME44xnKkC9WpOo5sIAqnV
xxl.job.executor.appname = cfw-message
xxl.job.executor.address = 
xxl.job.executor.ip = 
xxl.job.executor.port = 9219
xxl.job.executor.logpath = /data/ksyun/xxl-job/jobhandler
xxl.job.executor.logretentiondays = 30
kafka.consumer.servers = *************:9093
kafka.consumer.properties.group.id = cfw-message-consumer
kafka.consumer.properties.request.timeout = 180000
kafka.consumer.enable.auto.commit = false
kafka.consumer.auto.commit.interval = 1000
kafka.consumer.auto.offset.reset = latest
kafka.consumer.session.timeout = 120000
kafka.consumer.key-deserializer = org.apache.kafka.common.serialization.StringDeserializer
kafka.consumer.value-deserializer = org.apache.kafka.common.serialization.StringDeserializer
kafka.consumer.max.poll.interval.ms = 1000
kafka.consumer.max.poll.records = 500
kafka.consumer.concurrency = 4
kafka.listener.missing-topics-fatal = false
log.save.map = { "cfw-etcd-callback":2, "cfw-block": 180, "cfw-flow":180, "cfw-risk":180 }


