import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.SQLException;

/**
 * Database Connection Test Program
 * Used to verify MySQL database connection for CFW project
 */
public class DatabaseConnectionTest {

    // Database connection info from Apollo configuration
    private static final String DB_HOST = "*************";
    private static final String DB_PORT = "9102";
    private static final String DB_NAME = "cfw";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "consoleService8*";
    private static final String DB_DRIVER = "com.mysql.cj.jdbc.Driver";

    // Different URL configurations to test
    private static final String[] TEST_URLS = {
        // 1. Simplest URL
        "jdbc:mysql://" + DB_HOST + ":" + DB_PORT + "/" + DB_NAME,

        // 2. With SSL disabled
        "jdbc:mysql://" + DB_HOST + ":" + DB_PORT + "/" + DB_NAME + "?useSSL=false",

        // 3. With SSL disabled and timezone
        "jdbc:mysql://" + DB_HOST + ":" + DB_PORT + "/" + DB_NAME + "?useSSL=false&serverTimezone=Asia/Shanghai",

        // 4. With common parameters
        "jdbc:mysql://" + DB_HOST + ":" + DB_PORT + "/" + DB_NAME + "?useSSL=false&serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf8",

        // 5. Original Apollo configuration
        "jdbc:mysql://" + DB_HOST + ":" + DB_PORT + "/" + DB_NAME + "?useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true"
    };

    public static void main(String[] args) {
        System.out.println("=== CFW Database Connection Test (Multiple URLs) ===");
        System.out.println("Database Host: " + DB_HOST + ":" + DB_PORT);
        System.out.println("Database Name: " + DB_NAME);
        System.out.println("Username: " + DB_USER);
        System.out.println("Testing " + TEST_URLS.length + " different connection URLs...\n");

        // Test each URL configuration
        for (int i = 0; i < TEST_URLS.length; i++) {
            System.out.println("=== Test " + (i + 1) + "/" + TEST_URLS.length + " ===");
            System.out.println("URL: " + TEST_URLS[i]);
            testConnection(TEST_URLS[i]);
            System.out.println();
        }
    }

    private static void testConnection(String dbUrl) {
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        
        try {
            // 1. Load MySQL driver
            System.out.println("Loading MySQL driver...");
            Class.forName(DB_DRIVER);
            System.out.println("SUCCESS: MySQL driver loaded");

            // 2. Establish database connection
            System.out.println("Establishing database connection...");
            connection = DriverManager.getConnection(dbUrl, DB_USER, DB_PASSWORD);
            System.out.println("SUCCESS: Database connected!");

            // 3. Test basic database information
            System.out.println("Getting database basic information...");
            statement = connection.createStatement();

            // Get database version
            resultSet = statement.executeQuery("SELECT VERSION() as version");
            if (resultSet.next()) {
                System.out.println("MySQL Version: " + resultSet.getString("version"));
            }

            // Get current database name
            resultSet = statement.executeQuery("SELECT DATABASE() as db_name");
            if (resultSet.next()) {
                System.out.println("Current Database: " + resultSet.getString("db_name"));
            }

            // Get current time
            resultSet = statement.executeQuery("SELECT NOW() as current_time");
            if (resultSet.next()) {
                System.out.println("Database Time: " + resultSet.getString("current_time"));
            }

            // 4. Show first few tables
            System.out.println("Showing database tables...");
            resultSet = statement.executeQuery("SHOW TABLES");
            System.out.println("Database Tables:");
            int tableCount = 0;
            while (resultSet.next()) {
                tableCount++;
                System.out.println("   - " + resultSet.getString(1));
                if (tableCount >= 5) {
                    System.out.println("   ... (showing first 5 tables)");
                    break;
                }
            }

            System.out.println("SUCCESS: This URL works! Connection is working!");
            
        } catch (ClassNotFoundException e) {
            System.err.println("ERROR: MySQL driver not found: " + e.getMessage());
        } catch (SQLException e) {
            System.err.println("FAILED: " + e.getMessage());
            if (e.getMessage().contains("Communications link failure")) {
                System.err.println("Reason: Network connection issue");
            } else if (e.getMessage().contains("Access denied")) {
                System.err.println("Reason: Authentication failed");
            } else if (e.getMessage().contains("Unknown database")) {
                System.err.println("Reason: Database does not exist");
            } else if (e.getMessage().contains("SSL")) {
                System.err.println("Reason: SSL configuration issue");
            }
        } catch (Exception e) {
            System.err.println("ERROR: " + e.getMessage());
        } finally {
            // Close resources
            try {
                if (resultSet != null) resultSet.close();
                if (statement != null) statement.close();
                if (connection != null) connection.close();
            } catch (SQLException e) {
                // Ignore close errors
            }
        }
    }
}
