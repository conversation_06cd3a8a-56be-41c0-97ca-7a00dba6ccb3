import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.SQLException;

/**
 * Database Connection Test Program
 * Used to verify MySQL database connection for CFW project
 */
public class DatabaseConnectionTest {

    // Database connection info from Apollo configuration
    private static final String DB_URL = "*************************************************************************************************************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "consoleService8*";
    private static final String DB_DRIVER = "com.mysql.cj.jdbc.Driver";

    public static void main(String[] args) {
        System.out.println("=== CFW Database Connection Test ===");
        System.out.println("Database Host: *************:9102");
        System.out.println("Database Name: cfw");
        System.out.println("Username: " + DB_USER);
        System.out.println("Starting connection test...\n");
        
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        
        try {
            // 1. Load MySQL driver
            System.out.println("1. Loading MySQL driver...");
            Class.forName(DB_DRIVER);
            System.out.println("SUCCESS: MySQL driver loaded");

            // 2. Establish database connection
            System.out.println("\n2. Establishing database connection...");
            connection = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            System.out.println("SUCCESS: Database connected!");

            // 3. Test basic database information
            System.out.println("\n3. Getting database basic information...");
            statement = connection.createStatement();

            // Get database version
            resultSet = statement.executeQuery("SELECT VERSION() as version");
            if (resultSet.next()) {
                System.out.println("MySQL Version: " + resultSet.getString("version"));
            }

            // Get current database name
            resultSet = statement.executeQuery("SELECT DATABASE() as db_name");
            if (resultSet.next()) {
                System.out.println("Current Database: " + resultSet.getString("db_name"));
            }

            // Get current time
            resultSet = statement.executeQuery("SELECT NOW() as current_time");
            if (resultSet.next()) {
                System.out.println("Database Time: " + resultSet.getString("current_time"));
            }

            // 4. Show tables in database
            System.out.println("\n4. Showing database tables...");
            resultSet = statement.executeQuery("SHOW TABLES");
            System.out.println("Database Tables:");
            int tableCount = 0;
            while (resultSet.next()) {
                tableCount++;
                System.out.println("   - " + resultSet.getString(1));
                if (tableCount >= 10) {
                    System.out.println("   ... (showing first 10 tables)");
                    break;
                }
            }

            System.out.println("\nSUCCESS: Database connection test completed! Connection is working!");
            
        } catch (ClassNotFoundException e) {
            System.err.println("ERROR: MySQL driver not found: " + e.getMessage());
            System.err.println("Please ensure MySQL JDBC driver dependency is added");
        } catch (SQLException e) {
            System.err.println("ERROR: Database connection failed: " + e.getMessage());
            System.err.println("Error Code: " + e.getErrorCode());
            System.err.println("SQL State: " + e.getSQLState());

            // Common error solutions
            if (e.getMessage().contains("Communications link failure")) {
                System.err.println("\nPossible Solutions:");
                System.err.println("   1. Check network connection");
                System.err.println("   2. Verify database server address and port");
                System.err.println("   3. Check firewall settings");
            } else if (e.getMessage().contains("Access denied")) {
                System.err.println("\nPossible Solutions:");
                System.err.println("   1. Check username and password");
                System.err.println("   2. Verify user access permissions");
            }
        } catch (Exception e) {
            System.err.println("ERROR: Unknown error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 5. Close resources
            try {
                if (resultSet != null) resultSet.close();
                if (statement != null) statement.close();
                if (connection != null) connection.close();
                System.out.println("\nDatabase connection closed");
            } catch (SQLException e) {
                System.err.println("Error closing connection: " + e.getMessage());
            }
        }
    }
}
